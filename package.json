{"name": "@fk-deals-scraper/source", "version": "0.0.0", "license": "MIT", "scripts": {"build": "nx build", "test": "nx test", "lint": "nx workspace-lint && nx lint", "serve": "nx serve", "start": "nx serve", "build:flipkart-scraper": "nx build flipkart-scraper", "serve:flipkart-scraper": "nx serve flipkart-scraper", "start:flipkart-scraper": "nx start flipkart-scraper", "build:nextjs-app": "nx build nextjs-app", "serve:nextjs-app": "nx serve nextjs-app", "dev:nextjs-app": "nx serve nextjs-app", "affected:build": "nx affected:build", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "workspace-generator": "nx workspace-generator", "dep-graph": "nx dep-graph", "help": "nx help"}, "private": true, "devDependencies": {"@babel/preset-env": "^7.27.2", "@nx/eslint": "^21.2.0", "@nx/eslint-plugin": "^21.2.0", "@nx/jest": "^21.2.0", "@nx/js": "21.2.0", "@nx/next": "^21.2.0", "@nx/node": "^21.2.0", "@nx/workspace": "21.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.3", "@types/valid-url": "^1.0.7", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "babel-jest": "^30.0.0", "eslint-config-prettier": "^10.1.5", "jest-environment-jsdom": "^30.0.0", "nx": "21.2.0", "prettier": "^3.5.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}