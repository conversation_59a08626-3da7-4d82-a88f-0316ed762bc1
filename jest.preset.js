const nxPreset = require('@nx/jest/preset').default;

module.exports = {
  ...nxPreset,
  testMatch: [
    '<rootDir>/apps/**/*.(test|spec).{js,ts,tsx}',
    '<rootDir>/libs/**/*.(test|spec).{js,ts,tsx}'
  ],
  collectCoverageFrom: [
    'apps/**/*.{js,ts,tsx}',
    'libs/**/*.{js,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html']
};
