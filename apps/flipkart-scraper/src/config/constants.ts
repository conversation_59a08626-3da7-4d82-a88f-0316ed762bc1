// Application constants

export const REDIS_KEYS = {
  DATA: "data",
  SCRAPING_LINKS: "scrapingLinks",
  SCRAPING_SERVER_LINKS_MAP: "SCRAPING_SERVER_LINKS_MAP",
  ADDED_AT: "addedAt",
  DISABLED_PRODUCT_IDS: "disabledProductIds",
  DISABLED_PRODUCT: "disabledProductId",
  PRICE_MATCH_ALERT: "priceMatchAlert",
  PRICE_ALERTS: "price-alerts",
  LAST_UPDATED: "lastUpdated",
  BLOCKED_WORDS: "blockedWords",
  ADMIN_IDS: "adminIds",
  ALLOWED_CHAT_IDS: "allowedChatIds",
  ALERT_SENT: "alert-sent",
  PROXIES: "PROXIES",
  ALERT_SENT_COUNT: "ALERT_SENT_COUNT",
  IS_SCRAPER_RUNNING: "IS_SCRAPER_RUNNING",
} as const;

export const TIME_INTERVALS = {
  ONE_SECOND: 1000,
  TWO_SECONDS: 2000,
  THREE_SECONDS: 3000,
  FOUR_SECONDS: 4000,
  FIVE_SECONDS: 5000,
  TEN_SECONDS: 10000,
  FIFTEEN_SECONDS: 15000,
  THIRTY_SECONDS: 30000,
  ONE_MINUTE: 60000,
  TWO_MINUTES: 120000,
  THREE_MINUTES: 180000,
  FOUR_MINUTES: 240000,
  FIVE_MINUTES: 300000,
  TEN_MINUTES: 600000,
  FIFTEEN_MINUTES: 900000,
  THIRTY_MINUTES: 1800000,
  ONE_HOUR: 3600000,
  TWO_HOURS: 7200000,
  THREE_HOURS: 10800000,
  FOUR_HOURS: 14400000,
  FIVE_HOURS: 18000000,
  SIX_HOURS: 21600000,
  TWELVE_HOURS: 43200000,
  ONE_DAY: 86400000,
  ONE_MONTH: 2592000000,
} as const;

// Telegram API constants
export const TELEGRAM_BOT_API_URL = "https://api.telegram.org/bot";

export const TELEGRAM_COMMANDS = {
  SEND_MESSAGE: "sendMessage",
  SET_MY_COMMANDS: "setMyCommands",
};

export const SCRAPING_SELECTORS = {
  SIMPLE_PRODUCT_PAGE: {
    PRICE: "Nx9bqj",
    MRP: "yRaY8j",
    PRODUCT_NAME: "wjcEIp",
    RATINGS: "XQDdHH",
  },
  PRICE: "Nx9bqj",
  MRP: "yRaY8j",
  PACK_SIZE: ".BUOuZu",
  DISCOUNT: "UkUFwK",
  PRODUCT_LINK: ".DMMoT0",
  BRAND_NAME: "wjcEIp",
  PRODUCT_IMAGE: "_4WELSP",
  SPECS: "NqpwHC",
  STATUS: "DShtpz",
  STATUS_TEXT: "vfSpSs",
  TITLE_NODE: ".WOvzF4",
  HIGHLIGHT: "n5vj9c",
  IS_FK_ASSURED: "dVXNbG",
  RATINGS: "Y1HWO0",
  REVIEWS: "Wphh3N",
  IS_SPONSERED: "f8qK5m",
  IS_FLIPKART_CHOICE: "UzRoYO",
  OTHER_DETAILS: "otherDetails"
} as const;

export const DEFAULT_LOCALE = "en-IN";
export const DEFAULT_TIME_ZONE = "Asia/Kolkata";

export const ERROR_MESSAGES = {
  INVALID_URL: "Invalid URL",
  CONNECTION_ERROR: "Connection Error",
  REQUEST_ERROR: "Request Error",
  PARSE_ERROR: "Parse Error",
};

export const HTML_ATTRIBUTES = {
  CLASS: "class",
  TITLE: "title",
  HREF: "href",
  SRC: "src",
};

export const EVENTS = {
  SCRAPE: "scrape",
};

export const FLIPKART_URL = "https://flipkart.com";

export const DATA_PROPS = {
  PRODUCT_ID: "productId",
  PRODUCT_NAME: "productName",
  PRICE: "price",
  MRP: "mrp",
  DISCOUNT: "discount",
  PRODUCT_LINK: "productLink",
  BRAND_NAME: "brandName",
  PRODUCT_IMAGE: "productImage",
  PACK_SIZE: "packSize",
  STATUS: "status",
  IN_STOCK: "inStock",
  UPDATED_AT: "updatedAt",
  LISTING_ID: "listingId",
  HIGHLIGHT: "highlight",
  IS_FK_ASSURED: "isFkAssured",
  SPECS: "modelNo",
  RATINGS: "ratings",
  REVIEWS: "reviewsCount",
  IS_SPONSERED: "isSponsered",
  IS_FLIPKART_CHOICE: "isFlipkartChoice",
  OTHER_DETAILS: "otherDetails",
} as const;

export const DEFAULT_BLOCKED_WORDS = ["footox", "kanushi", "hellcat", "surya"];

export const AVAILABILITY_STATUSE = {
  CURRENTLY_UNAVAILABLE: "Currently unavailable",
  NOT_DELIVERABLE: "Not deliverable",
  IN_STOCK: "In Stock",
};

export const SYMBOLS = {
  RUPEE: "₹",
  PERCENTAGE: "%",
};

export const REGEX = {
  RUPEE_SYMBOL: /[₹,]/g,
  PERCENTAGE_SYMBOL: /off|%/g,
};

export const AUTHORIZED_USERS = {
  hardik: "9408763968",
  riyaz: "8142113499",
};

export const FILES = {
  RESTART_LOG: "restart.log",
};
