import dotenv from "dotenv";
import { Config } from "../types/index.js";

dotenv.config();

const prodConfig: Config = {
  redisHost: process.env.REDIS_HOST,
  redisPort: process.env.REDIS_PORT,
  redisPassword: process.env.REDIS_PASSWORD,
  port: process.env.PORT,
  serverKey: process.env.SCRAPING_SERVER_KEY,
};

const devConfig: Config = {
  redisHost: process.env.REDIS_HOST,
  redisPort: process.env.REDIS_PORT,
  port: process.env.PORT,
  serverKey: process.env.SCRAPING_SERVER_KEY,
};

export const config: Config =
  process.env.NODE_ENV === "production" ? prodConfig : devConfig;

// Default allowed users configuration
export const allowedUsers = [
  {
    UserName: "@hm58677",
    Id: "5054902069",
    First: "Hardik",
    Last: "M",
    Lang: "en",
  },
];
