import { EventEmitter } from "events";
import { ScraperEvents } from "../types/index.js";

class TypedEventEmitter extends EventEmitter {
  on<U extends keyof ScraperEvents>(event: U, listener: ScraperEvents[U]): this {
    return super.on(event, listener);
  }

  emit<U extends keyof ScraperEvents>(event: U, ...args: Parameters<ScraperEvents[U]>): boolean {
    return super.emit(event, ...args);
  }
}

const eventEmitter = new TypedEventEmitter();
export default eventEmitter;
