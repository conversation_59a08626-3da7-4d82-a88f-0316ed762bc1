import { ScrapingService } from "../services/ScrapingService.js";
import { ScrapingLink } from "../types/index.js";

export class ScrapingWorker {
  private scrapingService: ScrapingService;

  constructor() {
    this.scrapingService = new ScrapingService();
  }

  async scrape(link: ScrapingLink): Promise<void> {
    return this.scrapingService.scrape(link);
  }
}

// Export a default instance
export default new ScrapingWorker();
