// Core type definitions for the Flipkart Scraper

export interface ScrapingLink {
  title: string;
  key: string;
  category: string;
  url: string;
  scrapingSelectors: {
    key: string;
    querySelector: string;
    isMultiLevel: boolean;
  };
}

export interface ProductRecord {
  productId: string;
  productName: string;
  price: number;
  mrp?: number;
  discount?: number;
  productLink: string;
  brandName?: string;
  productImage?: string;
  packSize?: string;
  status: string;
  inStock: boolean;
  updatedAt: string;
  listingId?: string;
  highlight?: string;
  isFkAssured?: boolean | string;
  modelNo?: string;
  ratings?: string;
  reviewsCount?: string;
  isSponsered?: string;
  isFlipkartChoice?: string;
  otherDetails?: string;
}

export interface AllowedUser {
  UserName?: string;
  Id: string;
  First: string;
  Last?: string;
  Lang: string;
}

export interface Config {
  redisHost: string | undefined;
  redisPort: string | undefined;
  redisPassword?: string | undefined;
  port: string | undefined;
  serverKey: string | undefined;
}

export interface MessageOptions {
  productId: string;
  listingId?: string;
  price: number;
  oldPrice: number | string;
  updateStr: string;
  url: string;
  masterLinkName: string;
}

export interface ScraperEvents {
  scrape: (link: ScrapingLink) => void;
}

export interface RedisClientOptions {
  port?: number;
  host?: string;
  password?: string;
}
