"use strict";

import { ScrapingService } from "./services/ScrapingService.js";
import logger from "./utils/logger.js";
import { ScrapingLink } from "./types/index.js";

// Function to seed Redis data
async function seedRedisData(): Promise<void> {
  const scrapingService = new ScrapingService();
  
  try {
    await Promise.all([
      scrapingService.setAdminIds(),
      scrapingService.setBlockedWords(),
      scrapingService.setAllowedChatIds(),
      scrapingService.setScrapingLinks(),
    ]);
    
    logger.info("Redis data seeded successfully");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Failed to seed Redis data: ${errorMessage}`);
    throw error;
  }
}

// Function to scrape data
async function main(): Promise<void> {
  try {
    logger.info("Starting Flipkart Scraper application");
    
    // Seed Redis data
    await seedRedisData();

    const scrapingService = new ScrapingService();
    const links = await scrapingService.getLinksFromRedis();
    
    if (!links) {
      throw new Error("Failed to get links from Redis");
    }
    
    // Start scraping for each link
    for (const link of links) {
      try {
        console.log(`Starting scraping for link`, link.key);
        scrapingService.scrapeLink(link);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`Error scraping data from ${link.url}: ${errorMessage}`);
      }
    }
    
    logger.info("Scraping processes initiated for all links");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error({
      mainErrorMessage: errorMessage,
      error,
    });
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('Received SIGINT, shutting down gracefully');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

// Start the application
main().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});
