import axios, { AxiosResponse } from "axios";
import { load, CheerioAPI } from "cheerio";
import validUrl from "valid-url";
import dotenv from "dotenv";

import redisClient from "../repositories/RedisClient.js";
import logger from "../utils/logger.js";
import eventEmitter from "../events/eventEmitter.js";
import { config } from "../config/index.js";
import { links } from "../config/links.js";
import {
  REDIS_KEYS,
  DEFAULT_BLOCKED_WORDS,
  DEFAULT_LOCALE,
  DEFAULT_TIME_ZONE,
  FLIPKART_URL,
  HTML_ATTRIBUTES,
  SCRAPING_SELECTORS,
  TELEGRAM_BOT_API_URL,
  TELEGRAM_COMMANDS,
  TIME_INTERVALS,
  AVAILABILITY_STATUSE,
  REGEX,
  SYMBOLS,
  DATA_PROPS,
} from "../config/constants.js";
import {
  ScrapingLink,
  ProductRecord,
  MessageOptions,
} from "../types/index.js";

dotenv.config();

export class ScrapingService {
  constructor() {
    eventEmitter.on("scrape", async (link: ScrapingLink) => {
      this.scrape(link);
    });
  }

  async compareAndSendMessage(
    link: ScrapingLink,
    oldRecords: Record<string, ProductRecord>,
    newRecords: Record<string, ProductRecord>,
    priceAlertRecords: Record<string, string>
  ): Promise<void> {
    try {
      const blockedWords = await redisClient.smembers(REDIS_KEYS.BLOCKED_WORDS);
      for (let [productId, newRecord] of Object.entries(newRecords)) {
        if (!newRecord?.price) {
          console.error(
            "Invalid record. Price Missing",
            productId,
            `link :${link.key} : ${newRecord?.productName}`
          );
          continue;
        }

        const oldRecord = oldRecords[productId];
        if (!oldRecord) {
          await redisClient.hset(REDIS_KEYS.ADDED_AT, productId, this.getCurrentTime());
          continue;
        }

        const oldPrice = oldRecord.price;
        const newPrice = newRecord.price;

        if (oldPrice === newPrice) {
          continue;
        }

        const priceAlert = priceAlertRecords[productId];
        if (priceAlert && newPrice > parseInt(priceAlert)) {
          continue;
        }

        const isBlocked = await this.isProductBlocked(productId, newRecord, blockedWords);
        if (isBlocked) {
          continue;
        }

        const updateStr = newPrice < oldPrice ? "Price Drop" : "Price Increase";
        const messageOptions: MessageOptions = {
          productId,
          listingId: newRecord.listingId,
          price: newPrice,
          oldPrice,
          updateStr,
          url: newRecord.productLink,
          masterLinkName: link.title,
        };

        await this.sendMessage(messageOptions, newRecord);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("Error in compareAndSendMessage:", errorMessage);
    }
  }

  private async isProductBlocked(
    productId: string,
    record: ProductRecord,
    blockedWords: string[]
  ): Promise<boolean> {
    const isDisabled = await redisClient.sismember(REDIS_KEYS.DISABLED_PRODUCT_IDS, productId);
    if (isDisabled) return true;

    const isDisabledWithPattern = await redisClient.exists(`${REDIS_KEYS.DISABLED_PRODUCT}:${productId}_*`);
    if (isDisabledWithPattern) return true;

    const blockedWordsSet = new Set(blockedWords.map(word => word.toLowerCase()));
    const isBlackListedKeyword = Array.from(blockedWordsSet).some(word => {
      return (
        record?.brandName?.toLowerCase().includes(word) ||
        record?.productName?.toLowerCase().includes(word) ||
        record?.productLink?.toLowerCase().includes(word)
      );
    });

    return isBlackListedKeyword;
  }

  private async sendMessage(options: MessageOptions, record: ProductRecord): Promise<void> {
    try {
      // Implementation for sending Telegram messages would go here
      // This is a placeholder for the actual message sending logic
      console.log(`Sending message for product ${options.productId}: ${options.updateStr}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("Error sending message:", errorMessage);
    }
  }

  getCurrentTime(): string {
    return new Date().toLocaleString(DEFAULT_LOCALE, {
      timeZone: DEFAULT_TIME_ZONE,
      hour12: true,
      hour: "numeric",
      minute: "2-digit",
      second: "2-digit",
    });
  }

  async getResponse(url: string): Promise<AxiosResponse> {
    if (!validUrl.isUri(url)) {
      throw new Error("Invalid URL");
    }

    try {
      const response = await axios.get(url, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to fetch URL: ${errorMessage}`);
    }
  }

  async evaluatePage(cheerioRoot: CheerioAPI, link: ScrapingLink): Promise<Record<string, ProductRecord> | null> {
    try {
      const products: Record<string, ProductRecord> = {};
      const productElements = cheerioRoot(`.${link.scrapingSelectors.querySelector}`);

      productElements.each((index, element) => {
        try {
          const product = this.extractProductData(cheerioRoot, element, link);
          if (product && product.productId) {
            products[product.productId] = product;
          }
        } catch (error) {
          console.error(`Error extracting product at index ${index}:`, error);
        }
      });

      return Object.keys(products).length > 0 ? products : null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("Error evaluating page:", errorMessage);
      return null;
    }
  }

  private extractProductData(cheerioRoot: CheerioAPI, element: any, link: ScrapingLink): ProductRecord | null {
    try {
      const $element = cheerioRoot(element);
      
      // Extract basic product information
      const productLink = this.extractProductLink($element);
      if (!productLink) return null;

      const productId = this.extractProductId(productLink);
      if (!productId) return null;

      const productName = this.extractProductName($element);
      const price = this.extractPrice($element);
      const mrp = this.extractMRP($element);
      
      if (!price || price <= 0) return null;

      const product: ProductRecord = {
        productId,
        productName: productName || '',
        price,
        mrp,
        discount: this.calculateDiscount(price, mrp),
        productLink: `${FLIPKART_URL}${productLink}`,
        brandName: this.extractBrandName($element),
        productImage: this.extractProductImage($element),
        status: this.extractStatus($element),
        inStock: this.extractInStockStatus($element),
        updatedAt: this.getCurrentTime(),
        listingId: this.extractListingId(productLink),
        highlight: this.extractHighlight($element),
        isFkAssured: this.extractFkAssured($element),
        modelNo: this.extractModelNo($element),
        ratings: this.extractRatings($element),
        reviewsCount: this.extractReviewsCount($element),
      };

      return product;
    } catch (error) {
      console.error("Error extracting product data:", error);
      return null;
    }
  }

  private extractProductLink($element: any): string | null {
    const linkElement = $element.find(SCRAPING_SELECTORS.PRODUCT_LINK);
    return linkElement.attr(HTML_ATTRIBUTES.HREF) || null;
  }

  private extractProductId(productLink: string): string | null {
    const match = productLink.match(/pid=([^&]+)/);
    return match ? match[1] : null;
  }

  private extractProductName($element: any): string {
    return $element.find(`.${SCRAPING_SELECTORS.BRAND_NAME}`).text().trim();
  }

  private extractPrice($element: any): number {
    const priceText = $element.find(`.${SCRAPING_SELECTORS.PRICE}`).text().trim();
    const cleanPrice = priceText.replace(REGEX.RUPEE_SYMBOL, '');
    return parseInt(cleanPrice) || 0;
  }

  private extractMRP($element: any): number | undefined {
    const mrpText = $element.find(`.${SCRAPING_SELECTORS.MRP}`).text().trim();
    if (!mrpText) return undefined;
    const cleanMRP = mrpText.replace(REGEX.RUPEE_SYMBOL, '');
    return parseInt(cleanMRP) || undefined;
  }

  private calculateDiscount(price: number, mrp?: number): number | undefined {
    if (!mrp || mrp <= price) return undefined;
    return Math.round(((mrp - price) / mrp) * 100);
  }

  private extractBrandName($element: any): string | undefined {
    return $element.find(`.${SCRAPING_SELECTORS.BRAND_NAME}`).text().trim() || undefined;
  }

  private extractProductImage($element: any): string | undefined {
    const imgElement = $element.find(`.${SCRAPING_SELECTORS.PRODUCT_IMAGE}`);
    return imgElement.attr(HTML_ATTRIBUTES.SRC) || undefined;
  }

  private extractStatus($element: any): string {
    const statusElement = $element.find(`.${SCRAPING_SELECTORS.STATUS}`);
    return statusElement.text().trim() || AVAILABILITY_STATUSE.IN_STOCK;
  }

  private extractInStockStatus($element: any): boolean {
    const status = this.extractStatus($element);
    return status === AVAILABILITY_STATUSE.IN_STOCK;
  }

  private extractListingId(productLink: string): string | undefined {
    const match = productLink.match(/lid=([^&]+)/);
    return match ? match[1] : undefined;
  }

  private extractHighlight($element: any): string | undefined {
    return $element.find(`.${SCRAPING_SELECTORS.HIGHLIGHT}`).text().trim() || undefined;
  }

  private extractFkAssured($element: any): boolean {
    return $element.find(`.${SCRAPING_SELECTORS.IS_FK_ASSURED}`).length > 0;
  }

  private extractModelNo($element: any): string | undefined {
    return $element.find(`.${SCRAPING_SELECTORS.SPECS}`).text().trim() || undefined;
  }

  private extractRatings($element: any): string | undefined {
    return $element.find(`.${SCRAPING_SELECTORS.RATINGS}`).text().trim() || undefined;
  }

  private extractReviewsCount($element: any): string | undefined {
    return $element.find(`.${SCRAPING_SELECTORS.REVIEWS}`).text().trim() || undefined;
  }

  async scrape(link: ScrapingLink): Promise<void> {
    try {
      const curTime = this.getCurrentTime();
      console.log(`${curTime} => Scraping ${link.title}`);

      let response = null;
      try {
        response = await this.getResponse(link.url);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`${curTime} => Failed to process ${link.title}. ${errorMessage}`);
        setTimeout(() => this.scrapeLink(link), 10 * 1000);
        return;
      }

      if (!response) {
        console.error(`${curTime} => Failed to get response for ${link.title}.Retrying ...`);
        setTimeout(() => this.scrapeLink(link), 10 * 1000);
        return;
      }

      const cheerioRoot = load(response.data);
      const oldRecordsData = await redisClient.hget(REDIS_KEYS.DATA, `${link.key}`);
      let oldRecords = oldRecordsData ? JSON.parse(oldRecordsData) : {};
      let newRecords = await this.evaluatePage(cheerioRoot, link);

      if (!newRecords || Object.entries(newRecords).length === 0) {
        const curTime = this.getCurrentTime();
        console.error(`${curTime} => Failed to get data for page ${link.key}.Retrying .....`);
        await Promise.all([
          new Promise((resolve) => setTimeout(resolve, 5 * 1000)),
          this.scrapeLink(link),
        ]);
        return;
      }

      let isOldRecordsEmpty = !oldRecords || !Object.entries(oldRecords).length;
      if (isOldRecordsEmpty) {
        await redisClient.hset(REDIS_KEYS.DATA, `${link.key}`, JSON.stringify(newRecords));
        await this.scrapeLink(link);
        return;
      }

      const priceAlertRecords = await redisClient.hgetall(REDIS_KEYS.PRICE_ALERTS);
      await this.compareAndSendMessage(link, oldRecords, newRecords, priceAlertRecords);

      oldRecords = { ...oldRecords, ...newRecords };

      await Promise.all([
        redisClient.hset(REDIS_KEYS.DATA, `${link.key}`, JSON.stringify(oldRecords)),
        redisClient.set(REDIS_KEYS.LAST_UPDATED, this.getCurrentTime()),
      ]);

      this.scrapeLink(link);
    } catch (error) {
      this.scrapeLink(link);
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack?.split("\n") : [];
      console.error({
        scrapeError: errorMessage,
        error,
        errorData: errorStack,
      });
    }
  }

  scrapeLink(link: ScrapingLink): void {
    setTimeout(() => {
      eventEmitter.emit("scrape", link);
    }, TIME_INTERVALS.THIRTY_SECONDS);
  }

  async getLinksFromRedis(): Promise<ScrapingLink[] | null> {
    try {
      if (!config.serverKey) {
        throw new Error("Server key is not configured");
      }

      const linksData = await redisClient.hget(REDIS_KEYS.SCRAPING_LINKS, config.serverKey);
      if (!linksData) {
        console.log("No links found in Redis, using default links");
        return links;
      }

      return JSON.parse(linksData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("Error getting links from Redis:", errorMessage);
      return null;
    }
  }

  async setScrapingLinks(): Promise<void> {
    if (!config.serverKey) {
      throw new Error("Server key is not configured");
    }
    await redisClient.hset(REDIS_KEYS.SCRAPING_LINKS, config.serverKey, JSON.stringify(links));
  }

  async setBlockedWords(): Promise<void> {
    const words = DEFAULT_BLOCKED_WORDS;
    await Promise.all(
      words.map((word) => redisClient.sadd(REDIS_KEYS.BLOCKED_WORDS, word))
    );
  }

  async setAdminIds(): Promise<void> {
    // Implementation for setting admin IDs
    console.log("Setting admin IDs...");
  }

  async setAllowedChatIds(): Promise<void> {
    // Implementation for setting allowed chat IDs
    console.log("Setting allowed chat IDs...");
  }
}
