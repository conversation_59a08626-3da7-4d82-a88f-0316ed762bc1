import Redis from "ioredis";
import { ScrapingService } from "./ScrapingService.js";
import logger from "../utils/logger.js";
import { config } from "../config/index.js";

export class LinkSyncService {
  private subscriber: Redis;
  private scrapingService: ScrapingService;
  private restartCallback?: () => void;

  constructor(redisOptions: any, restartCallback?: () => void) {
    this.subscriber = new Redis(redisOptions);
    this.scrapingService = new ScrapingService();
    this.restartCallback = restartCallback;
    
    this.setupSubscriptions();
  }

  private setupSubscriptions(): void {
    // Subscribe to link changes
    this.subscriber.subscribe('link_changes', (err, count) => {
      if (err) {
        logger.error('Failed to subscribe to link_changes:', err);
        return;
      }
      logger.info(`Subscribed to ${count} Redis channels for link sync`);
    });

    // Handle incoming messages
    this.subscriber.on('message', async (channel, message) => {
      try {
        if (channel === 'link_changes') {
          await this.handleLinkChange(JSON.parse(message));
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error('Error handling Redis message:', errorMessage);
      }
    });

    this.subscriber.on('error', (err) => {
      logger.error('Redis subscriber error:', err);
    });
  }

  private async handleLinkChange(changeData: any): Promise<void> {
    const { action, serverKey, link, linkKey, timestamp } = changeData;
    
    logger.info(`Received link change: ${action} for server ${serverKey} at ${timestamp}`);

    // Only process changes for our server
    if (serverKey !== config.serverKey) {
      logger.info(`Ignoring change for different server: ${serverKey}`);
      return;
    }

    try {
      switch (action) {
        case 'add':
          await this.handleLinkAdd(link);
          break;
        case 'delete':
          await this.handleLinkDelete(linkKey);
          break;
        default:
          logger.warn(`Unknown link change action: ${action}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Error handling link ${action}:`, errorMessage);
    }
  }

  private async handleLinkAdd(link: any): Promise<void> {
    logger.info(`Processing link addition: ${link.title} (${link.key})`);
    
    try {
      // Sync the new link to the backend file
      await this.scrapingService.syncLinksToFile();
      logger.info('Successfully synced links to file after addition');
      
      // Trigger restart to pick up new links
      await this.triggerRestart('Link added');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('Error handling link addition:', errorMessage);
    }
  }

  private async handleLinkDelete(linkKey: string): Promise<void> {
    logger.info(`Processing link deletion: ${linkKey}`);
    
    try {
      // Note: We don't sync deletes to the file - deleted links remain in file but are filtered out
      logger.info('Link marked as deleted - will be filtered out from scraping');
      
      // Trigger restart to stop scraping the deleted link
      await this.triggerRestart('Link deleted');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('Error handling link deletion:', errorMessage);
    }
  }

  private async triggerRestart(reason: string): Promise<void> {
    logger.info(`Triggering application restart: ${reason}`);
    
    // Give some time for any ongoing operations to complete
    setTimeout(() => {
      if (this.restartCallback) {
        this.restartCallback();
      } else {
        // Fallback: exit process to trigger restart by process manager
        logger.info('No restart callback provided, exiting process');
        process.exit(0);
      }
    }, 2000); // 2 second delay
  }

  public async close(): Promise<void> {
    try {
      await this.subscriber.quit();
      logger.info('Link sync service closed');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('Error closing link sync service:', errorMessage);
    }
  }
}
