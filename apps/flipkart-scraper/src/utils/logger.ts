import winston, { format } from "winston";
const { timestamp, json, colorize, printf } = format;

const logger = winston.createLogger({
  level: "debug",
  format: format.combine(
    format.label({ label: "flipkart-scraper", message: false }),
    timestamp({
      format: "YYYY-MM-DD HH:mm:ss",
    }),
    json(),
    colorize({
      colors: {
        debug: "cyan",
        info: "green",
        error: "red",
        warn: "yellow",
      },
      all: true,
    }),
    printf((info: any) => {
      return `${info.timestamp} ${info.level}: ${info.message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      filename: "logs/error/error.log",
    }),
    new winston.transports.File({ filename: "logs/combinedlog.log" }),
  ],
});

// Process unhandled promise rejections
process.on("unhandledRejection", (error: unknown) => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logger.error(`Unhandled Rejection: ${errorMessage}`);
  process.exit(1);
});

// Process uncaught exceptions
process.on("uncaughtException", (error: Error) => {
  logger.error(`Uncaught Exception: ${error.message}`);
  process.exit(1);
});

export default logger;
