{"name": "flipkart-scraper", "version": "1.0.0", "description": "", "main": "src/main.ts", "type": "module", "scripts": {"build": "tsc", "dev": "nodemon --exec \"node --loader ts-node/esm\" src/main.ts", "start": "node --loader ts-node/esm src/main.ts", "start:prod": "pm2 start dist/main.js --name flipkart-scraper", "stop": "pm2 stop all", "test": "jest", "clean": "rm -rf dist", "prebuild": "npm run clean"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.4.0", "bullmq": "^5.10.4", "cheerio": "1.0.0-rc.12", "dom-serializer": "^2.0.0", "dotenv": "^16.1.4", "ejs": "^3.1.9", "express": "^4.18.2", "express-basic-auth": "^1.2.1", "header-generator": "^2.1.52", "ioredis": "^5.3.2", "puppeteer": "^20.8.2", "valid-url": "^1.0.9", "winston": "^3.13.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/valid-url": "^1.0.7", "globals": "^15.8.0", "nodemon": "^2.0.22", "jest": "^29.5.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}