# Flipkart Scraper

A well-organized Node.js application for scraping product data from <PERSON>lipkart with price monitoring and alert capabilities.

## 🏗️ Project Structure

```
src/
├── config/           # Configuration files
│   ├── index.ts      # Main configuration
│   ├── constants.ts  # Application constants
│   └── links.ts      # Scraping links configuration
├── types/            # TypeScript type definitions
│   └── index.ts      # Core types
├── services/         # Business logic services
│   └── ScrapingService.ts  # Main scraping service
├── repositories/     # Data access layer
│   └── RedisClient.ts      # Redis client wrapper
├── utils/            # Utility functions
│   └── logger.ts     # Logging utility
├── workers/          # Background workers
│   └── ScrapingWorker.ts   # Scraping worker
├── events/           # Event handling
│   └── eventEmitter.ts     # Event emitter
└── main.ts           # Application entry point
```

## 🚀 Features

- **Modular Architecture**: Clean separation of concerns with organized directory structure
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Redis Integration**: Efficient data storage and retrieval
- **Event-Driven**: Asynchronous scraping with event emitters
- **Price Monitoring**: Track price changes and send alerts
- **Configurable**: Environment-based configuration management
- **Logging**: Comprehensive logging with Winston
- **Error Handling**: Robust error handling and recovery

## 📦 Installation

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration
```

## 🔧 Configuration

Create a `.env` file with the following variables:

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
PORT=3000
SCRAPING_SERVER_KEY=your_server_key
NODE_ENV=development
```

## 🏃‍♂️ Running the Application

```bash
# Development mode with hot reload
npm run dev

# Production mode
npm run build
npm start

# Using PM2 for production
npm run start:prod

# Stop PM2 processes
npm run stop
```