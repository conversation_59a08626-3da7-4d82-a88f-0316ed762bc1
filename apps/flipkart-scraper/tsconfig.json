{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "ESNext", "target": "ES2020", "moduleResolution": "node", "allowJs": true, "outDir": "./dist", "rootDir": "./src", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/config/*": ["config/*"], "@/services/*": ["services/*"], "@/repositories/*": ["repositories/*"], "@/utils/*": ["utils/*"], "@/workers/*": ["workers/*"], "@/events/*": ["events/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "tests", "**/*.spec.ts", "**/*.test.ts"]}