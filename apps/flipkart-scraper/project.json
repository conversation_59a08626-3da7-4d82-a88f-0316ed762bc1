{"name": "flipkart-scraper", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/flipkart-scraper", "projectType": "application", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/flipkart-scraper", "main": "apps/flipkart-scraper/index.ts", "tsConfig": "apps/flipkart-scraper/tsconfig.app.json", "assets": ["apps/flipkart-scraper/*.md"]}}, "serve": {"executor": "nx:run-commands", "options": {"command": "node --loader ts-node/esm index.ts", "cwd": "apps/flipkart-scraper"}}, "dev": {"executor": "nx:run-commands", "options": {"command": "pnpm dev", "cwd": "apps/flipkart-scraper"}}, "start": {"executor": "nx:run-commands", "options": {"command": "node --loader ts-node/esm index.ts", "cwd": "apps/flipkart-scraper"}}, "install": {"executor": "nx:run-commands", "options": {"command": "pnpm install", "cwd": "apps/flipkart-scraper"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/flipkart-scraper/**/*.{js,ts}", "!apps/flipkart-scraper/node_modules/**/*"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/flipkart-scraper/jest.config.js", "passWithNoTests": true}}}, "tags": ["type:app", "scope:scraper"]}