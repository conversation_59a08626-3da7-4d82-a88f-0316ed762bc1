{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*", "node_modules/**/*", "dist/**/*"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {}}, {"files": ["*.spec.js", "*.test.js", "*.spec.ts", "*.test.ts"], "env": {"jest": true}}], "env": {"node": true, "es6": true}}