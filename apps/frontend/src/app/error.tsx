'use client'

import { useEffect } from 'react'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center p-6">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-red-600 mb-4">Oops!</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">Something went wrong</h2>
          <p className="text-gray-600">
            An unexpected error occurred. Please try again.
          </p>
        </div>

        <div className="space-y-4">
          <button
            onClick={() => reset()}
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Try Again
          </button>

          <div className="text-sm text-gray-500">
            <p>If the problem persists, please contact support.</p>
            {error.digest && (
              <p className="mt-2 font-mono text-xs bg-gray-100 p-2 rounded">
                Error ID: {error.digest}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
