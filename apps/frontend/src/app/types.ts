export interface Product {
  inStock: boolean;
  productId: string;
  productLink: string;
  listingId: string;
  productImage: string;
  status: string;
  updatedAt: string;
  productName: string;
  brandName: string;
  modelNo: string;
  ratings: string;
  reviewsCount: string;
  price: number;
  mrp: number;
  discount: number;
  highlight?: string;
  isFkAssured?: boolean;
  // Additional fields from Redis
  priceAlert?: string;
  addedAt?: string;
  isBlocked?: boolean;
  isBlackListedKeyword?: boolean;
}

export interface ProductData {
  [productId: string]: Product;
}

export interface TableColumn {
  key: keyof Product;
  label: string;
  sortable: boolean;
  type: 'text' | 'number' | 'boolean' | 'image' | 'link' | 'currency' | 'percentage';
}

export interface SortConfig {
  key: keyof Product;
  direction: 'asc' | 'desc';
}

export interface FilterConfig {
  search: string;
  inStock: boolean | null;
  priceRange: {
    min: number;
    max: number;
  };
  discountRange: {
    min: number;
    max: number;
  };
}

export interface PaginationConfig {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
}

export interface ScrapingLink {
  key: string;
  title: string;
  category: string;
  url: string;
  scrapingSelectors: {
    key: string;
    isMultiLevel: boolean;
    querySelector: string;
  };
}

export interface AvailableKey {
  serverKey: string;
  links: ScrapingLink[];
}

export interface LinkFormData {
  title: string;
  url: string;
  categoryKey: string;
  isMultiLevel: boolean;
  serverKey: string;
}

export interface LinksApiResponse {
  links: AvailableKey[];
  activeKey: string;
  maxLinksLimit: number;
  data: ScrapingLink[];
}

export interface ProductsApiResponse {
  products: Product[];
  lastUpdateTime: string;
  activeKey: string;
  availableKeys: AvailableKey[];
  totalProducts: number;
}

export interface KeysApiResponse {
  availableKeys: AvailableKey[];
  defaultKey: string | null;
}
