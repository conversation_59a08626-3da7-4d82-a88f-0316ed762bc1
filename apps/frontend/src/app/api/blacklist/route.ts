import { NextRequest, NextResponse } from 'next/server';
import redisClient from '@/lib/redis';

export async function GET() {
  try {
    const blockedWords = await redisClient.smembers('blockedWords');
    return NextResponse.json({ blockedWords: blockedWords.sort() });
  } catch (error) {
    console.error('Error fetching blacklist:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blacklist' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { word } = await request.json();
    
    if (!word || typeof word !== 'string' || word.trim().length === 0) {
      return NextResponse.json({ error: 'Invalid word' }, { status: 400 });
    }

    const trimmedWord = word.trim().toLowerCase();
    await redisClient.sadd('blockedWords', trimmedWord);
    
    return NextResponse.json({ message: 'Word added to blacklist successfully' });
  } catch (error) {
    console.error('Error adding to blacklist:', error);
    return NextResponse.json(
      { error: 'Failed to add word to blacklist' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const word = searchParams.get('word');
    
    if (!word) {
      return NextResponse.json({ error: 'Word parameter is required' }, { status: 400 });
    }

    await redisClient.srem('blockedWords', word);
    
    return NextResponse.json({ message: 'Word removed from blacklist successfully' });
  } catch (error) {
    console.error('Error removing from blacklist:', error);
    return NextResponse.json(
      { error: 'Failed to remove word from blacklist' },
      { status: 500 }
    );
  }
}
