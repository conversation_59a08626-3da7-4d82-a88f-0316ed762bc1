import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

export async function GET() {
  try {
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ pincodes: [] });
    }

    const pincodes = await redisClient.smembers('pincodes');
    return NextResponse.json({ pincodes: pincodes.sort() });
  } catch (error) {
    console.error('Error fetching pincodes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pincodes' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    const { pincode } = await request.json();

    if (!pincode || isNaN(pincode) || pincode.toString().length !== 6) {
      return NextResponse.json({ error: 'Invalid pincode. Must be a 6-digit number.' }, { status: 400 });
    }

    await redisClient.sadd('pincodes', pincode.toString());

    return NextResponse.json({ message: 'Pincode added successfully' });
  } catch (error) {
    console.error('Error adding pincode:', error);
    return NextResponse.json(
      { error: 'Failed to add pincode' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const pincode = searchParams.get('pincode');

    if (!pincode) {
      return NextResponse.json({ error: 'Pincode parameter is required' }, { status: 400 });
    }

    await redisClient.srem('pincodes', pincode);

    return NextResponse.json({ message: 'Pincode removed successfully' });
  } catch (error) {
    console.error('Error removing pincode:', error);
    return NextResponse.json(
      { error: 'Failed to remove pincode' },
      { status: 500 }
    );
  }
}
