import { NextRequest, NextResponse } from 'next/server';
import redisClient from '@/lib/redis';

export async function POST(request: NextRequest) {
  try {
    const { action, productId, key, priceAlert } = await request.json();

    switch (action) {
      case 'block':
        await redisClient.sadd('disabledProductIds', productId);
        await redisClient.setex(`disabledProductIds:${productId}_${Date.now()}`, 86400, '1');
        return NextResponse.json({ message: 'Product blocked successfully' });

      case 'unblock': {
        await redisClient.srem('disabledProductIds', productId);
        const keysToDelete = await redisClient.keys(`disabledProductIds:${productId}*`);
        if (keysToDelete.length > 0) {
          await redisClient.del(keysToDelete);
        }
        const alertKeys = await redisClient.keys(`ALERT_SENT_COUNT:${productId}*`);
        if (alertKeys.length > 0) {
          await redisClient.del(alertKeys);
        }
        return NextResponse.json({ message: 'Product unblocked successfully' });
      }

      case 'setPriceAlert':
        if (!priceAlert || isNaN(priceAlert)) {
          return NextResponse.json({ error: 'Invalid price alert value' }, { status: 400 });
        }
        await redisClient.hset('price-alerts', productId, priceAlert);
        return NextResponse.json({ message: 'Price alert set successfully' });

      case 'removePriceAlert':
        await redisClient.hdel('price-alerts', productId);
        return NextResponse.json({ message: 'Price alert removed successfully' });

      case 'resetData':
        if (!key) {
          return NextResponse.json({ error: 'Key is required for reset' }, { status: 400 });
        }
        await redisClient.hdel('data', key);
        return NextResponse.json({ message: 'Data reset successfully' });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error performing product action:', error);
    return NextResponse.json(
      { error: 'Failed to perform action' },
      { status: 500 }
    );
  }
}
