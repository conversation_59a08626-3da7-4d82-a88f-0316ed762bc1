import { NextRequest, NextResponse } from 'next/server';
import redisClient from '@/lib/redis';
import { Product } from '@/app/types';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const key = searchParams.get('key');

    // Test Redis connection
    try {
      await redisClient.ping();
    } catch (redisError: any) {
      console.error('Redis connection failed:', redisError.message);
      return NextResponse.json({
        error: 'Database connection failed. Please check Redis connection.',
        details: redisError.message
      }, { status: 503 });
    }

    // Get scraping links to determine the key if not provided
    const scrapingLinksObj = (await redisClient.hgetall('scrapingLinks')) || {};

    // Use provided key or default to first available key
    const dataKey = key || (Object.values(scrapingLinksObj)[0] ? JSON.parse(Object.values(scrapingLinksObj)[0])[0]?.key : null);

    if (!dataKey) {
      return NextResponse.json({ error: 'No data key available' }, { status: 404 });
    }

    // Get main product data
    const rawData = await redisClient.hget('data', dataKey);
    let data: Record<string, Product> = {};

    if (rawData) {
      data = JSON.parse(rawData);
    }

    // Get additional data for enrichment
    const [blockedWords, disabledProducts, priceAlertRecords] = await Promise.all([
      redisClient.smembers('blockedWords'),
      redisClient.smembers('disabledProductIds'),
      redisClient.hgetall('price-alerts'),
    ]);

    // Get disabled product IDs with pattern
    const disabledProductIds = await redisClient.keys('disabledProductIds:*');
    const disabledProductIdsSet = new Set(
      disabledProductIds.map((productId) => productId.split(':')[1]?.split('_')[0]).filter(Boolean)
    );

    const blockedWordsSet = new Set(blockedWords.map((word) => word.toLowerCase()));
    const disabledProductsSet = new Set(disabledProducts);

    // Get last update time
    const lastUpdateTime = Object.values(data)[0]?.updatedAt || 'Not Available';

    // Process and enrich product data
    const responseData = await Promise.all(
      Object.entries(data).map(async ([, product]) => {
        // Get additional product info
        const addedAt = await redisClient.hget('addedAt', product.productId) || ' - ';

        const enrichedProduct = {
          ...product,
          priceAlert: priceAlertRecords[product.productId] || ' - ',
          addedAt,
          isBlocked: disabledProductsSet.has(product.productId) || disabledProductIdsSet.has(product.productId),
          isBlackListedKeyword: blockedWordsSet.size > 0 && Array.from(blockedWordsSet).some((word) => {
            return (
              product?.brandName?.toLowerCase().includes(word) ||
              product?.productName?.toLowerCase().includes(word) ||
              product?.productLink?.toLowerCase().includes(word)
            );
          }),
        };

        // Remove fields that were in the original Node.js response
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { brandName, listingId, updatedAt, ...responseProduct } = enrichedProduct;

        return responseProduct;
      })
    );

    // Get available keys for key switching
    const availableKeys = Object.entries(scrapingLinksObj).map(([serverKey, linksJson]) => {
      const links = JSON.parse(linksJson);
      return {
        serverKey,
        links: links.map((link: any) => ({
          key: link.key,
          title: link.title,
          category: link.category,
        })),
      };
    });

    return NextResponse.json({
      products: responseData,
      lastUpdateTime,
      activeKey: dataKey,
      availableKeys,
      totalProducts: responseData.length,
    });

  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
