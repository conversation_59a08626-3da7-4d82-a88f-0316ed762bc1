import { NextResponse } from 'next/server';
import redisClient from '@/lib/redis';

export async function GET() {
  try {
    // Get scraping links configuration
    const scrapingLinksObj = (await redisClient.hgetall('scrapingLinks')) || {};
    
    const availableKeys = Object.entries(scrapingLinksObj).map(([serverKey, linksJson]) => {
      const links = JSON.parse(linksJson);
      return {
        serverKey,
        links: links.map((link: any) => ({
          key: link.key,
          title: link.title,
          category: link.category,
          url: link.url,
        })),
      };
    });

    return NextResponse.json({
      availableKeys,
      defaultKey: availableKeys[0]?.links[0]?.key || null,
    });

  } catch (error) {
    console.error('Error fetching keys:', error);
    return NextResponse.json(
      { error: 'Failed to fetch keys' },
      { status: 500 }
    );
  }
}
