import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

export async function POST(request: NextRequest) {
  try {
    const { action, serverKey, linkData } = await request.json();

    // Get Redis client
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    switch (action) {
      case 'sync_to_backend': {
        // Notify backend to sync links to file
        await redisClient.publish('link_sync', JSON.stringify({
          action: 'sync_to_file',
          serverKey,
          timestamp: new Date().toISOString()
        }));
        
        return NextResponse.json({ message: 'Sync request sent to backend' });
      }

      case 'add_link': {
        // Add link and notify backend
        const scrapingLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');
        
        const newLink = {
          ...linkData,
          deleted: false, // Add deleted flag
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        scrapingLinks.push(newLink);
        await redisClient.hset('scrapingLinks', serverKey, JSON.stringify(scrapingLinks));
        
        // Notify backend to sync to file
        await redisClient.publish('link_sync', JSON.stringify({
          action: 'add_to_file',
          serverKey,
          link: newLink,
          timestamp: new Date().toISOString()
        }));
        
        return NextResponse.json({ message: 'Link added and sync initiated', link: newLink });
      }

      case 'soft_delete': {
        const { linkKey } = await request.json();
        
        // Get existing links
        const scrapingLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');
        
        // Find and mark as deleted
        const linkIndex = scrapingLinks.findIndex((link: any) => link.key === linkKey);
        if (linkIndex === -1) {
          return NextResponse.json({ error: 'Link not found' }, { status: 404 });
        }
        
        scrapingLinks[linkIndex].deleted = true;
        scrapingLinks[linkIndex].deletedAt = new Date().toISOString();
        scrapingLinks[linkIndex].updatedAt = new Date().toISOString();
        
        await redisClient.hset('scrapingLinks', serverKey, JSON.stringify(scrapingLinks));
        
        // Note: We don't sync deletes to backend file - they remain there
        return NextResponse.json({ message: 'Link soft deleted' });
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in link sync:', error);
    return NextResponse.json(
      { error: 'Failed to sync links' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const serverKey = searchParams.get('serverKey');

    if (!serverKey) {
      return NextResponse.json({ error: 'Server key is required' }, { status: 400 });
    }

    // Get Redis client
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    // Get all links (including deleted ones for admin view)
    const allLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');
    
    // Get active links (non-deleted)
    const activeLinks = allLinks.filter((link: any) => !link.deleted);
    
    return NextResponse.json({
      activeLinks,
      allLinks,
      totalLinks: allLinks.length,
      activeCount: activeLinks.length,
      deletedCount: allLinks.length - activeLinks.length
    });

  } catch (error) {
    console.error('Error fetching sync status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sync status' },
      { status: 500 }
    );
  }
}
