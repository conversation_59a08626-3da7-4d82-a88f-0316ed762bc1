import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryKey = searchParams.get('key');

    // Get Redis client
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({
        links: [],
        activeKey: '',
        maxLinksLimit: parseInt(process.env.MAX_LINKS_LIMIT || '50'),
        data: [],
      });
    }

    // Get scraping links configuration
    const scrapingLinksObj = (await redisClient.hgetall('scrapingLinks')) || {};
    const activeKey = queryKey || Object.keys(scrapingLinksObj)[0] || '';

    if (!activeKey) {
      return NextResponse.json({
        links: [],
        activeKey: '',
        maxLinksLimit: parseInt(process.env.MAX_LINKS_LIMIT || '50'),
        data: [],
      });
    }

    // Parse the data for the active key and filter out deleted links
    const allData = scrapingLinksObj[activeKey] ? JSON.parse(scrapingLinksObj[activeKey]) : [];
    const data = allData.filter((link: any) => !link.deleted);

    // Format all available keys, filtering out deleted links
    const links = Object.entries(scrapingLinksObj).map(([serverKey, scrapingLinksJson]) => {
      const allScrapingLinks = JSON.parse(scrapingLinksJson);
      const activeScrapingLinks = allScrapingLinks.filter((link: any) => !link.deleted);
      return {
        serverKey,
        links: activeScrapingLinks,
        totalLinks: allScrapingLinks.length,
        activeLinks: activeScrapingLinks.length,
        deletedLinks: allScrapingLinks.length - activeScrapingLinks.length,
      };
    });

    return NextResponse.json({
      links,
      activeKey,
      maxLinksLimit: parseInt(process.env.MAX_LINKS_LIMIT || '50'),
      data,
    });

  } catch (error) {
    console.error('Error fetching links:', error);
    return NextResponse.json(
      { error: 'Failed to fetch links' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { title, url, categoryKey, isMultiLevel, serverKey } = await request.json();

    // Validation
    if (!title || !url || !categoryKey || !serverKey) {
      return NextResponse.json(
        { error: 'Missing required fields: title, url, categoryKey, serverKey' },
        { status: 400 }
      );
    }

    // Get Redis client
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    // Generate unique identifier key
    const identifierKey = `${categoryKey}-${Math.random().toString(36).substr(2, 5)}`;

    // Get existing links for the server
    const scrapingLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');

    // Create new link object with metadata
    const newLink = {
      scrapingSelectors: {
        key: categoryKey.toLowerCase().replace(/\s/g, ''),
        isMultiLevel: Boolean(isMultiLevel),
        querySelector: '_75nlfW',
      },
      category: categoryKey.toLowerCase().replace(/\s/g, ''),
      key: identifierKey,
      url,
      title,
      deleted: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add to links array
    scrapingLinks.push(newLink);

    // Save back to Redis
    await redisClient.hset('scrapingLinks', serverKey, JSON.stringify(scrapingLinks));

    // Notify backend to sync and restart
    try {
      await redisClient.publish('link_changes', JSON.stringify({
        action: 'add',
        serverKey,
        link: newLink,
        timestamp: new Date().toISOString()
      }));
    } catch (publishError) {
      console.error('Failed to publish link change:', publishError);
    }

    return NextResponse.json({ message: 'Link added successfully', link: newLink });

  } catch (error) {
    console.error('Error adding link:', error);
    return NextResponse.json(
      { error: 'Failed to add link' },
      { status: 500 }
    );
  }
}
