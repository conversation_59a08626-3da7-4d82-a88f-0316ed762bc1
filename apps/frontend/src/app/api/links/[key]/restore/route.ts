import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params;
    const { serverKey } = await request.json();

    if (!serverKey) {
      return NextResponse.json({ error: 'Server key is required' }, { status: 400 });
    }

    // Get Redis client
    const redisClient = getRedisClient();
    if (!redisClient) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    const scrapingLinks = JSON.parse(await redisClient.hget('scrapingLinks', serverKey) || '[]');
    const linkIndex = scrapingLinks.findIndex((link: any) => link.key === key);

    if (linkIndex === -1) {
      return NextResponse.json({ error: 'Link not found' }, { status: 404 });
    }

    const link = scrapingLinks[linkIndex];
    if (!link.deleted) {
      return NextResponse.json({ error: 'Link is not deleted' }, { status: 400 });
    }

    // Restore the link
    scrapingLinks[linkIndex].deleted = false;
    scrapingLinks[linkIndex].restoredAt = new Date().toISOString();
    scrapingLinks[linkIndex].updatedAt = new Date().toISOString();
    delete scrapingLinks[linkIndex].deletedAt;
    
    await redisClient.hset('scrapingLinks', serverKey, JSON.stringify(scrapingLinks));
    
    // Notify backend to restart and sync
    try {
      await redisClient.publish('link_changes', JSON.stringify({
        action: 'add', // Treat restore as add
        serverKey,
        link: scrapingLinks[linkIndex],
        timestamp: new Date().toISOString()
      }));
    } catch (publishError) {
      console.error('Failed to publish link change:', publishError);
    }

    return NextResponse.json({ message: 'Link restored successfully' });

  } catch (error) {
    console.error('Error restoring link:', error);
    return NextResponse.json(
      { error: 'Failed to restore link' },
      { status: 500 }
    );
  }
}
