import Redis from 'ioredis';

let redisClient: Redis | null = null;

function createRedisClient(): Redis {
  if (!redisClient) {
    const config: {
      host: string;
      port: number;
      password?: string;
      retryStrategy: (times: number) => number;
    } = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      retryStrategy(times: number) {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
    };

    // Only add password if it exists
    if (process.env.REDIS_PASSWORD) {
      config.password = process.env.REDIS_PASSWORD;
    }

    redisClient = new Redis(config);

    redisClient.on('connect', () => {
      console.log('Connected to Redis');
    });

    redisClient.on('error', (err) => {
      console.log('Redis Client Error', err);
    });
  }

  return redisClient;
}

export default createRedisClient();
