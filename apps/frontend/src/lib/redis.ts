import Redis from 'ioredis';

let redisClient: Redis | null = null;
let isConnecting = false;

function createRedisClient(): Redis | null {
  // Don't create Redis client in browser
  if (typeof window !== 'undefined') {
    return null;
  }

  // Don't create Redis client during build time (when Next.js is generating static pages)
  if (process.env.NEXT_PHASE === 'phase-production-build') {
    return null;
  }

  // Prevent multiple simultaneous connection attempts
  if (isConnecting) {
    return redisClient;
  }

  if (!redisClient) {
    try {
      isConnecting = true;

      const config: {
        host: string;
        port: number;
        password?: string;
        retryStrategy: (times: number) => number;
        lazyConnect: boolean;
        maxRetriesPerRequest: number;
      } = {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        lazyConnect: true, // Don't connect immediately
        maxRetriesPerRequest: 3,
        retryStrategy(times: number) {
          const delay = Math.min(times * 50, 2000);
          return delay;
        },
      };

      // Only add password if it exists
      if (process.env.REDIS_PASSWORD) {
        config.password = process.env.REDIS_PASSWORD;
      }

      redisClient = new Redis(config);

      redisClient.on('connect', () => {
        console.log('Connected to Redis');
        isConnecting = false;
      });

      redisClient.on('error', (err) => {
        console.log('Redis Client Error', err);
        isConnecting = false;
      });

      redisClient.on('close', () => {
        console.log('Redis connection closed');
        isConnecting = false;
      });

    } catch (error) {
      console.error('Failed to create Redis client:', error);
      isConnecting = false;
      return null;
    }
  }

  return redisClient;
}

export function getRedisClient(): Redis | null {
  return createRedisClient();
}

// For backward compatibility, but don't initialize immediately
export default null;
