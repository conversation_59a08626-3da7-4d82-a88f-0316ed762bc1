'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { TrashIcon, PlusIcon, RefreshCwIcon } from 'lucide-react';

const AdminPanel: React.FC = () => {
  const [blacklistedWords, setBlacklistedWords] = useState<string[]>([]);
  const [pincodes, setPincodes] = useState<string[]>([]);
  const [newWord, setNewWord] = useState('');
  const [newPincode, setNewPincode] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchBlacklistedWords = async () => {
    try {
      const response = await fetch('/api/blacklist');
      if (response.ok) {
        const data = await response.json();
        setBlacklistedWords(data.blockedWords);
      }
    } catch (error) {
      console.error('Error fetching blacklisted words:', error);
    }
  };

  const fetchPincodes = async () => {
    try {
      const response = await fetch('/api/pincodes');
      if (response.ok) {
        const data = await response.json();
        setPincodes(data.pincodes);
      }
    } catch (error) {
      console.error('Error fetching pincodes:', error);
    }
  };

  useEffect(() => {
    fetchBlacklistedWords();
    fetchPincodes();
  }, []);

  const addBlacklistedWord = async () => {
    if (!newWord.trim()) return;
    
    try {
      setLoading(true);
      const response = await fetch('/api/blacklist', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ word: newWord.trim() }),
      });
      
      if (response.ok) {
        setNewWord('');
        fetchBlacklistedWords();
      } else {
        let errorMessage = 'Failed to add word';
        try {
          const error = await response.json();
          errorMessage = error.error || 'Failed to add word';
        } catch (parseError) {
          console.warn('Could not parse error response as JSON:', parseError);
          errorMessage = `Failed to add word: ${response.statusText}`;
        }
        alert(errorMessage);
      }
    } catch (error) {
      console.error('Error adding word:', error);
      alert('Failed to add word');
    } finally {
      setLoading(false);
    }
  };

  const removeBlacklistedWord = async (word: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/blacklist?word=${encodeURIComponent(word)}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        fetchBlacklistedWords();
      } else {
        let errorMessage = 'Failed to remove word';
        try {
          const error = await response.json();
          errorMessage = error.error || 'Failed to remove word';
        } catch (parseError) {
          console.warn('Could not parse error response as JSON:', parseError);
          errorMessage = `Failed to remove word: ${response.statusText}`;
        }
        alert(errorMessage);
      }
    } catch (error) {
      console.error('Error removing word:', error);
      alert('Failed to remove word');
    } finally {
      setLoading(false);
    }
  };

  const addPincode = async () => {
    if (!newPincode.trim() || newPincode.length !== 6 || isNaN(Number(newPincode))) {
      alert('Please enter a valid 6-digit pincode');
      return;
    }
    
    try {
      setLoading(true);
      const response = await fetch('/api/pincodes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pincode: newPincode }),
      });
      
      if (response.ok) {
        setNewPincode('');
        fetchPincodes();
      } else {
        let errorMessage = 'Failed to add pincode';
        try {
          const error = await response.json();
          errorMessage = error.error || 'Failed to add pincode';
        } catch (parseError) {
          console.warn('Could not parse error response as JSON:', parseError);
          errorMessage = `Failed to add pincode: ${response.statusText}`;
        }
        alert(errorMessage);
      }
    } catch (error) {
      console.error('Error adding pincode:', error);
      alert('Failed to add pincode');
    } finally {
      setLoading(false);
    }
  };

  const removePincode = async (pincode: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/pincodes?pincode=${encodeURIComponent(pincode)}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        fetchPincodes();
      } else {
        let errorMessage = 'Failed to remove pincode';
        try {
          const error = await response.json();
          errorMessage = error.error || 'Failed to remove pincode';
        } catch (parseError) {
          console.warn('Could not parse error response as JSON:', parseError);
          errorMessage = `Failed to remove pincode: ${response.statusText}`;
        }
        alert(errorMessage);
      }
    } catch (error) {
      console.error('Error removing pincode:', error);
      alert('Failed to remove pincode');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Admin Panel
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => {
              fetchBlacklistedWords();
              fetchPincodes();
            }}
            disabled={loading}
          >
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="blacklist" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="blacklist">Blacklisted Words</TabsTrigger>
            <TabsTrigger value="pincodes">Pincodes</TabsTrigger>
          </TabsList>
          
          <TabsContent value="blacklist" className="space-y-4">
            <div className="flex space-x-2">
              <Input
                placeholder="Enter word to blacklist..."
                value={newWord}
                onChange={(e) => setNewWord(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addBlacklistedWord()}
              />
              <Button onClick={addBlacklistedWord} disabled={loading || !newWord.trim()}>
                <PlusIcon className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Total blacklisted words: {blacklistedWords.length}
              </p>
              <div className="flex flex-wrap gap-2 max-h-60 overflow-y-auto">
                {blacklistedWords.map((word) => (
                  <Badge key={word} variant="secondary" className="flex items-center space-x-1">
                    <span>{word}</span>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <button className="ml-1 hover:text-red-600">
                          <TrashIcon className="h-3 w-3" />
                        </button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Remove Blacklisted Word</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to remove "{word}" from the blacklist?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => removeBlacklistedWord(word)}>
                            Remove
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </Badge>
                ))}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="pincodes" className="space-y-4">
            <div className="flex space-x-2">
              <Input
                placeholder="Enter 6-digit pincode..."
                value={newPincode}
                onChange={(e) => setNewPincode(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addPincode()}
                maxLength={6}
              />
              <Button onClick={addPincode} disabled={loading || !newPincode.trim()}>
                <PlusIcon className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Total pincodes: {pincodes.length}
              </p>
              <div className="flex flex-wrap gap-2 max-h-60 overflow-y-auto">
                {pincodes.map((pincode) => (
                  <Badge key={pincode} variant="outline" className="flex items-center space-x-1">
                    <span>{pincode}</span>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <button className="ml-1 hover:text-red-600">
                          <TrashIcon className="h-3 w-3" />
                        </button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Remove Pincode</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to remove pincode "{pincode}"?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => removePincode(pincode)}>
                            Remove
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </Badge>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AdminPanel;
