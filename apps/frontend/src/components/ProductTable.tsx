'use client';

'use client';

import React, { useState, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Product, SortConfig, FilterConfig, PaginationConfig } from '@/app/types';
import {
  ChevronUpIcon,
  ChevronDownIcon,
  MoreHorizontalIcon,
  BanIcon,
  CheckCircleIcon,
  BellIcon,
  BellOffIcon
} from 'lucide-react';

interface ProductTableProps {
  products: Product[];
  onProductUpdate?: () => void;
  activeKey?: string;
}

const ProductTable: React.FC<ProductTableProps> = ({ products, onProductUpdate, activeKey }) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'productName', direction: 'asc' });
  const [filterConfig, setFilterConfig] = useState<FilterConfig>({
    search: '',
    inStock: null,
    priceRange: { min: 0, max: 50000 },
    discountRange: { min: 0, max: 100 },
  });
  const [pagination, setPagination] = useState<PaginationConfig>({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 0,
  });
  const [priceAlertDialog, setPriceAlertDialog] = useState<{ open: boolean; productId: string; currentAlert: string }>({
    open: false,
    productId: '',
    currentAlert: '',
  });
  const [priceAlertValue, setPriceAlertValue] = useState('');
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Filter and sort products
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter((product) => {
      const searchTerm = filterConfig.search.toLowerCase();
      const matchesSearch = filterConfig.search === '' ||
        (product.productName && product.productName.toLowerCase().includes(searchTerm)) ||
        (product.brandName && product.brandName.toLowerCase().includes(searchTerm)) ||
        (product.modelNo && product.modelNo.toLowerCase().includes(searchTerm));

      const matchesStock = filterConfig.inStock === null || product.inStock === filterConfig.inStock;
      const matchesPrice = product.price >= filterConfig.priceRange.min && product.price <= filterConfig.priceRange.max;
      const matchesDiscount = product.discount >= filterConfig.discountRange.min && product.discount <= filterConfig.discountRange.max;

      return matchesSearch && matchesStock && matchesPrice && matchesDiscount;
    });

    // Sort products
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      
      if (sortConfig.direction === 'asc') {
        return aStr < bStr ? -1 : aStr > bStr ? 1 : 0;
      } else {
        return aStr > bStr ? -1 : aStr < bStr ? 1 : 0;
      }
    });

    return filtered;
  }, [products, sortConfig, filterConfig]);

  // Update pagination when filtered products change
  React.useEffect(() => {
    setPagination(prev => ({
      ...prev,
      totalItems: filteredAndSortedProducts.length,
      totalPages: Math.ceil(filteredAndSortedProducts.length / prev.itemsPerPage),
      currentPage: 1, // Reset to first page when filters change
    }));
  }, [filteredAndSortedProducts.length, pagination.itemsPerPage]);

  // Get current page products
  const currentPageProducts = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.itemsPerPage;
    const endIndex = startIndex + pagination.itemsPerPage;
    return filteredAndSortedProducts.slice(startIndex, endIndex);
  }, [filteredAndSortedProducts, pagination.currentPage, pagination.itemsPerPage]);

  const handleSort = (key: keyof Product) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatRating = (rating: string) => {
    return `${rating} ⭐`;
  };

  const performProductAction = async (action: string, productId: string, additionalData?: any) => {
    try {
      setActionLoading(productId);
      const response = await fetch('/api/products/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          productId,
          key: activeKey,
          ...additionalData,
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Action failed';
        try {
          const error = await response.json();
          errorMessage = error.error || 'Action failed';
        } catch (parseError) {
          console.warn('Could not parse action error response as JSON:', parseError);
          errorMessage = `Action failed: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();

      // Show success message (you can implement toast notifications here)
      console.log(result.message);

      // Refresh the product list
      if (onProductUpdate) {
        onProductUpdate();
      }
    } catch (error) {
      console.error('Action failed:', error);
      // Show error message (you can implement toast notifications here)
      alert(error instanceof Error ? error.message : 'Action failed');
    } finally {
      setActionLoading(null);
    }
  };

  const handleBlockProduct = (productId: string) => {
    performProductAction('block', productId);
  };

  const handleUnblockProduct = (productId: string) => {
    performProductAction('unblock', productId);
  };

  const handleSetPriceAlert = () => {
    if (!priceAlertValue || isNaN(Number(priceAlertValue))) {
      alert('Please enter a valid price');
      return;
    }
    performProductAction('setPriceAlert', priceAlertDialog.productId, { priceAlert: Number(priceAlertValue) });
    setPriceAlertDialog({ open: false, productId: '', currentAlert: '' });
    setPriceAlertValue('');
  };

  const handleRemovePriceAlert = (productId: string) => {
    performProductAction('removePriceAlert', productId);
  };

  const openPriceAlertDialog = (productId: string, currentAlert: string) => {
    setPriceAlertDialog({ open: true, productId, currentAlert });
    setPriceAlertValue(currentAlert !== ' - ' ? currentAlert : '');
  };

  const SortableHeader = ({ column, children }: { column: keyof Product; children: React.ReactNode }) => {
    return (
      <TableHead
        className="cursor-pointer hover:bg-gray-50 select-none"
        onClick={() => handleSort(column)}
      >
        <div className="flex items-center space-x-1">
          <span>{children}</span>
          {sortConfig.key === column && (
            sortConfig.direction === 'asc' ?
              <ChevronUpIcon className="h-4 w-4" /> :
              <ChevronDownIcon className="h-4 w-4" />
          )}
        </div>
      </TableHead>
    );
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters & Search</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Search Products</label>
              <Input
                placeholder="Search by name, brand, or model..."
                value={filterConfig.search}
                onChange={(e) => setFilterConfig(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Stock Status</label>
              <Select
                value={filterConfig.inStock === null ? 'all' : filterConfig.inStock.toString()}
                onValueChange={(value) => 
                  setFilterConfig(prev => ({ 
                    ...prev, 
                    inStock: value === 'all' ? null : value === 'true' 
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Products</SelectItem>
                  <SelectItem value="true">In Stock</SelectItem>
                  <SelectItem value="false">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Items per Page</label>
              <Select
                value={pagination.itemsPerPage.toString()}
                onValueChange={(value) => 
                  setPagination(prev => ({ 
                    ...prev, 
                    itemsPerPage: parseInt(value),
                    currentPage: 1 
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 per page</SelectItem>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="25">25 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Results</label>
              <div className="text-sm text-gray-600 py-2">
                Showing {currentPageProducts.length} of {filteredAndSortedProducts.length} products
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          <div className="w-full">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">Image</TableHead>
                  <SortableHeader column="productName">
                    <div className="max-w-xs">Product Details</div>
                  </SortableHeader>
                  <SortableHeader column="price">Price & Discount</SortableHeader>
                  <SortableHeader column="ratings">Rating & Reviews</SortableHeader>
                  <TableHead className="w-32">Status & Alerts</TableHead>
                  <TableHead className="w-32">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentPageProducts.map((product) => (
                  <TableRow
                    key={product.productId}
                    className={`hover:bg-gray-50 ${
                      product.isBlocked ? 'bg-red-50 border-l-4 border-red-500' :
                      product.isBlackListedKeyword ? 'bg-yellow-50 border-l-4 border-yellow-500' : ''
                    }`}
                  >
                    <TableCell>
                      <div className="relative w-12 h-12">
                        <Image
                          src={product.productImage}
                          alt={product.productName}
                          fill
                          className="object-cover rounded-md"
                          sizes="48px"
                        />
                      </div>
                    </TableCell>
                    <TableCell className="max-w-md">
                      <div className="space-y-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Link
                                href={product.productLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="product-title-link font-medium text-sm line-clamp-2 block hover:text-blue-600 transition-colors"
                                style={{ maxWidth: '300px' }}
                              >
                                {product.productName}
                              </Link>
                            </TooltipTrigger>
                            <TooltipContent side="top" className="max-w-xs">
                              <p className="text-sm">{product.productName}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <div className="text-xs text-gray-500">{product.brandName} • {product.modelNo}</div>
                        <div className="flex flex-wrap gap-1">
                          {product.highlight && (
                            <Badge variant="secondary" className="text-xs">
                              {product.highlight}
                            </Badge>
                          )}
                          {product.isBlocked && (
                            <Badge variant="destructive" className="text-xs">
                              Blocked
                            </Badge>
                          )}
                          {product.isBlackListedKeyword && (
                            <Badge variant="outline" className="text-xs border-yellow-500 text-yellow-700">
                              Blacklisted
                            </Badge>
                          )}
                          {product.isFkAssured && (
                            <Badge variant="outline" className="text-xs">
                              Assured
                            </Badge>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-semibold text-green-600 text-lg">
                          {formatCurrency(product.price)}
                        </div>
                        <div className="text-xs text-gray-500 line-through">
                          MRP: {formatCurrency(product.mrp)}
                        </div>
                        <Badge variant="destructive" className="text-xs">
                          {product.discount}% OFF
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm font-medium">
                          {formatRating(product.ratings)}
                        </div>
                        <div className="text-xs text-gray-600">
                          {product.reviewsCount} reviews
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-2">
                        <Badge variant={product.inStock ? "default" : "secondary"} className="text-xs">
                          {product.status}
                        </Badge>
                        {product.priceAlert && product.priceAlert !== ' - ' ? (
                          <div className="flex items-center space-x-1">
                            <BellIcon className="h-3 w-3 text-blue-500" />
                            <span className="text-xs text-blue-600">₹{product.priceAlert}</span>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-400">No alert</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 px-2"
                              disabled={actionLoading === product.productId}
                            >
                              <MoreHorizontalIcon className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />

                            {product.isBlocked ? (
                              <DropdownMenuItem onClick={() => handleUnblockProduct(product.productId)}>
                                <CheckCircleIcon className="h-4 w-4 mr-2" />
                                Unblock
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => handleBlockProduct(product.productId)}>
                                <BanIcon className="h-4 w-4 mr-2" />
                                Block
                              </DropdownMenuItem>
                            )}

                            <DropdownMenuSeparator />

                            <DropdownMenuItem
                              onClick={() => openPriceAlertDialog(product.productId, product.priceAlert || ' - ')}
                            >
                              <BellIcon className="h-4 w-4 mr-2" />
                              Price Alert
                            </DropdownMenuItem>

                            {product.priceAlert && product.priceAlert !== ' - ' && (
                              <DropdownMenuItem onClick={() => handleRemovePriceAlert(product.productId)}>
                                <BellOffIcon className="h-4 w-4 mr-2" />
                                Remove Alert
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      <Card>
        <CardContent className="flex items-center justify-between p-4">
          <div className="text-sm text-gray-600">
            Page {pagination.currentPage} of {pagination.totalPages}
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.currentPage === 1}
              onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage - 1 }))}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.currentPage === pagination.totalPages}
              onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage + 1 }))}
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Price Alert Dialog */}
      <Dialog open={priceAlertDialog.open} onOpenChange={(open) => setPriceAlertDialog(prev => ({ ...prev, open }))}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Set Price Alert</DialogTitle>
            <DialogDescription>
              Set a price alert for this product. You'll be notified when the price drops to or below this amount.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="price" className="text-right">
                Price (₹)
              </label>
              <Input
                id="price"
                type="number"
                value={priceAlertValue}
                onChange={(e) => setPriceAlertValue(e.target.value)}
                className="col-span-3"
                placeholder="Enter target price"
              />
            </div>
            {priceAlertDialog.currentAlert !== ' - ' && (
              <p className="text-sm text-gray-600">
                Current alert: ₹{priceAlertDialog.currentAlert}
              </p>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setPriceAlertDialog({ open: false, productId: '', currentAlert: '' })}
            >
              Cancel
            </Button>
            <Button type="button" onClick={handleSetPriceAlert}>
              Set Alert
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductTable;
