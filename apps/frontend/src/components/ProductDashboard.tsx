'use client';

import React, { useState, useEffect } from 'react';
import ProductTable from './ProductTable';
import AdminPanel from './AdminPanel';
import LinksManagement from './LinksManagement';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { RefreshCwIcon, AlertCircleIcon, TrashIcon } from 'lucide-react';
import { Product, ProductsApiResponse, AvailableKey } from '@/app/types';

const ProductDashboard: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('');
  const [activeKey, setActiveKey] = useState<string>('');
  const [availableKeys, setAvailableKeys] = useState<AvailableKey[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<number>(10); // Default 10 seconds

  const fetchProducts = async (key?: string) => {
    try {
      setLoading(true);
      setError(null);

      const url = key ? `/api/products?key=${encodeURIComponent(key)}` : '/api/products';
      const response = await fetch(url);

      if (!response.ok) {
        // Try to parse error response as JSON, fallback to status text
        let errorMessage = `Failed to fetch products: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
          if (errorData.details) {
            errorMessage += ` (${errorData.details})`;
          }
        } catch (parseError) {
          // If JSON parsing fails, use the status text
          console.warn('Could not parse error response as JSON:', parseError);
        }
        throw new Error(errorMessage);
      }

      const data: ProductsApiResponse = await response.json();

      setProducts(data.products);
      setLastUpdateTime(data.lastUpdateTime);
      setActiveKey(data.activeKey);
      setAvailableKeys(data.availableKeys);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch products');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyChange = (newKey: string) => {
    if (newKey !== activeKey) {
      fetchProducts(newKey);
    }
  };

  const handleRefresh = () => {
    if (activeKey) {
      fetchProducts(activeKey);
    } else {
      fetchProducts();
    }
  };

  const handleResetData = async () => {
    if (!activeKey) {
      alert('No active key selected');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/products/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resetData',
          key: activeKey,
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Reset failed';
        try {
          const error = await response.json();
          errorMessage = error.error || 'Reset failed';
        } catch (parseError) {
          console.warn('Could not parse reset error response as JSON:', parseError);
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      alert(result.message);

      // Refresh the product list
      fetchProducts(activeKey);
    } catch (error) {
      console.error('Reset failed:', error);
      alert(error instanceof Error ? error.message : 'Reset failed');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchProducts();
  }, []);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !activeKey) return;

    const interval = setInterval(() => {
      fetchProducts(activeKey);
    }, refreshInterval * 1000); // Convert seconds to milliseconds

    return () => clearInterval(interval);
  }, [autoRefresh, activeKey, refreshInterval]);

  // Get all available keys for the dropdown
  const allKeys = availableKeys.flatMap(keyGroup => 
    keyGroup.links.map(link => ({
      value: link.key,
      label: `${link.title} (${keyGroup.serverKey})`,
      category: link.category,
    }))
  );

  if (loading && products.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCwIcon className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-500" />
          <p className="text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  if (error && products.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md">
            <AlertCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Database Connection Failed</h3>
            <p className="text-gray-600 mb-4">
              Unable to connect to Redis database. Please check your connection and try again.
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-500">{error}</p>
              <Button onClick={handleRefresh} variant="outline">
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Retry Connection
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Tabs defaultValue="products" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="products">Products Dashboard</TabsTrigger>
        <TabsTrigger value="links">Links Management</TabsTrigger>
        <TabsTrigger value="admin">Admin Panel</TabsTrigger>
      </TabsList>

      <TabsContent value="products" className="space-y-6">
        {/* Control Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Data Source & Controls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 items-end">
            <div>
              <label className="text-sm font-medium mb-2 block">Data Source</label>
              <Select value={activeKey} onValueChange={handleKeyChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select data source..." />
                </SelectTrigger>
                <SelectContent>
                  {allKeys.map((key) => (
                    <SelectItem key={key.value} value={key.value}>
                      {key.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Auto Refresh</label>
              <Button
                variant={autoRefresh ? "default" : "outline"}
                onClick={() => setAutoRefresh(!autoRefresh)}
                className="w-full"
              >
                {autoRefresh ? "Auto Refresh ON" : "Auto Refresh OFF"}
              </Button>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Interval (seconds)</label>
              <Input
                type="number"
                min="1"
                max="300"
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(Math.max(1, parseInt(e.target.value) || 10))}
                className="w-full"
                placeholder="10"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Manual Refresh</label>
              <Button
                onClick={handleRefresh}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                {loading ? (
                  <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCwIcon className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Reset Data</label>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" className="w-full" disabled={!activeKey}>
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Reset Data
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Reset Data</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to reset all data for &quot;{activeKey}&quot;? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleResetData}>
                      Reset Data
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <div className="space-y-1">
                <Badge variant={error ? "destructive" : "default"}>
                  {error ? "Error" : "Connected"}
                </Badge>
                {lastUpdateTime && (
                  <p className="text-xs text-gray-500">
                    Last update: {lastUpdateTime}
                  </p>
                )}
              </div>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Product Table */}
      <ProductTable
        products={products}
        onProductUpdate={handleRefresh}
        activeKey={activeKey}
      />
    </TabsContent>

    <TabsContent value="links" className="space-y-6">
      <LinksManagement />
    </TabsContent>

    <TabsContent value="admin" className="space-y-6">
      <AdminPanel />
    </TabsContent>
  </Tabs>
  );
};

export default ProductDashboard;
