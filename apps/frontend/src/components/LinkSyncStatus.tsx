'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, ArrowRightLeft, Trash2, RotateCcw } from 'lucide-react';

interface SyncStatus {
  activeLinks: any[];
  allLinks: any[];
  totalLinks: number;
  activeCount: number;
  deletedCount: number;
}

interface LinkSyncStatusProps {
  serverKey: string;
}

export function LinkSyncStatus({ serverKey }: LinkSyncStatusProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);

  const fetchSyncStatus = async () => {
    if (!serverKey) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/links/sync?serverKey=${serverKey}`);
      if (response.ok) {
        const data = await response.json();
        setSyncStatus(data);
      }
    } catch (error) {
      console.error('Error fetching sync status:', error);
    } finally {
      setLoading(false);
    }
  };

  const triggerSync = async () => {
    setSyncing(true);
    try {
      const response = await fetch('/api/links/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'sync_to_backend',
          serverKey
        })
      });
      
      if (response.ok) {
        // Refresh status after sync
        setTimeout(fetchSyncStatus, 1000);
      }
    } catch (error) {
      console.error('Error triggering sync:', error);
    } finally {
      setSyncing(false);
    }
  };

  const restoreDeletedLink = async (linkKey: string) => {
    try {
      // This would need to be implemented - restore a deleted link
      const response = await fetch(`/api/links/${linkKey}/restore`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ serverKey })
      });
      
      if (response.ok) {
        fetchSyncStatus();
      }
    } catch (error) {
      console.error('Error restoring link:', error);
    }
  };

  useEffect(() => {
    fetchSyncStatus();
  }, [serverKey]);

  if (!syncStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRightLeft className="h-5 w-5" />
            Link Sync Status
          </CardTitle>
          <CardDescription>
            Loading sync status...
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRightLeft className="h-5 w-5" />
            Link Sync Status
            <Button
              variant="outline"
              size="sm"
              onClick={fetchSyncStatus}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
          <CardDescription>
            Manage link synchronization between frontend and backend
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {syncStatus.activeCount}
              </div>
              <div className="text-sm text-gray-600">Active Links</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {syncStatus.deletedCount}
              </div>
              <div className="text-sm text-gray-600">Deleted Links</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {syncStatus.totalLinks}
              </div>
              <div className="text-sm text-gray-600">Total Links</div>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={triggerSync}
              disabled={syncing}
              className="flex items-center gap-2"
            >
              <ArrowRightLeft className={`h-4 w-4 ${syncing ? 'animate-spin' : ''}`} />
              {syncing ? 'Syncing...' : 'Sync to Backend'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {syncStatus.deletedCount > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5" />
              Deleted Links
            </CardTitle>
            <CardDescription>
              Links that are hidden from frontend but preserved in backend
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {syncStatus.allLinks
                .filter(link => link.deleted)
                .map((link) => (
                  <div
                    key={link.key}
                    className="flex items-center justify-between p-3 border rounded-lg bg-gray-50"
                  >
                    <div>
                      <div className="font-medium text-gray-700">{link.title}</div>
                      <div className="text-sm text-gray-500">
                        Deleted: {new Date(link.deletedAt).toLocaleString()}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">Deleted</Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => restoreDeletedLink(link.key)}
                        className="flex items-center gap-1"
                      >
                        <RotateCcw className="h-3 w-3" />
                        Restore
                      </Button>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
