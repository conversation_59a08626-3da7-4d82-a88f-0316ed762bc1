'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { PlusIcon, PencilIcon, TrashIcon, RefreshCwIcon, ExternalLinkIcon } from 'lucide-react';
import { LinksApiResponse, ScrapingLink, LinkFormData } from '@/app/types';

const LinksManagement: React.FC = () => {
  const [linksData, setLinksData] = useState<LinksApiResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeKey, setActiveKey] = useState<string>('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingLink, setEditingLink] = useState<ScrapingLink | null>(null);
  const [formData, setFormData] = useState<LinkFormData>({
    title: '',
    url: '',
    categoryKey: '',
    isMultiLevel: false,
    serverKey: '',
  });
  const [formErrors, setFormErrors] = useState<{
    title?: string;
    url?: string;
    categoryKey?: string;
    serverKey?: string;
  }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [, setDeletingLinkKey] = useState<string | null>(null);

  // Validation functions
  const validateUrl = (url: string): string | null => {
    if (!url.trim()) return 'URL is required';
    try {
      new URL(url);
      return null;
    } catch {
      return 'Please enter a valid URL';
    }
  };

  const validateTitle = (title: string): string | null => {
    if (!title.trim()) return 'Title is required';
    if (title.length > 100) return 'Title must be less than 100 characters';
    return null;
  };

  const validateCategoryKey = (categoryKey: string): string | null => {
    if (!categoryKey.trim()) return 'Category key is required';
    if (!/^[a-zA-Z0-9\s]+$/.test(categoryKey)) return 'Category key can only contain letters, numbers, and spaces';
    if (categoryKey.length > 50) return 'Category key must be less than 50 characters';
    return null;
  };

  const validateForm = (): boolean => {
    const errors: typeof formErrors = {};

    const titleError = validateTitle(formData.title);
    if (titleError) errors.title = titleError;

    const urlError = validateUrl(formData.url);
    if (urlError) errors.url = urlError;

    const categoryError = validateCategoryKey(formData.categoryKey);
    if (categoryError) errors.categoryKey = categoryError;

    if (!formData.serverKey) errors.serverKey = 'Server selection is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const fetchLinks = async (key?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const url = key ? `/api/links?key=${key}` : '/api/links';
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch links');
      }

      const data: LinksApiResponse = await response.json();
      setLinksData(data);
      setActiveKey(data.activeKey);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch links');
      console.error('Error fetching links:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLinks();
  }, []);

  const handleKeyChange = (newKey: string) => {
    if (newKey !== activeKey) {
      setActiveKey(newKey);
      fetchLinks(newKey);
    }
  };

  const handleRefresh = () => {
    fetchLinks(activeKey);
  };

  const openAddForm = () => {
    if (!linksData || linksData.data.length >= linksData.maxLinksLimit) {
      alert(`You can't add more than ${linksData?.maxLinksLimit || 50} links.`);
      return;
    }

    setEditingLink(null);
    setFormData({
      title: '',
      url: '',
      categoryKey: '',
      isMultiLevel: false,
      serverKey: activeKey,
    });
    setFormErrors({});
    setSuccessMessage(null);
    setIsFormOpen(true);
  };

  const openEditForm = (link: ScrapingLink) => {
    setEditingLink(link);
    setFormData({
      title: link.title,
      url: link.url,
      categoryKey: link.scrapingSelectors.key,
      isMultiLevel: link.scrapingSelectors.isMultiLevel,
      serverKey: activeKey,
    });
    setFormErrors({});
    setSuccessMessage(null);
    setIsFormOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (editingLink) {
        // Update existing link
        const response = await fetch(`/api/links/${editingLink.key}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            url: formData.url,
            title: formData.title,
            scrapingSelectors: {
              key: formData.categoryKey,
              isMultiLevel: formData.isMultiLevel,
            },
            serverKey: activeKey,
            newServerKey: formData.serverKey !== activeKey ? formData.serverKey : '',
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to update link');
        }
      } else {
        // Add new link
        const response = await fetch('/api/links', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to add link');
        }
      }

      setIsFormOpen(false);
      setFormErrors({});
      setSuccessMessage(editingLink ? 'Link updated successfully!' : 'Link added successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
      handleRefresh();
    } catch (err) {
      console.error('Form submission error:', err);
      alert(err instanceof Error ? err.message : 'Operation failed');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (linkKey: string) => {
    try {
      setDeletingLinkKey(linkKey);
      const response = await fetch(`/api/links/${linkKey}?key=${activeKey}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to delete link');
      }

      setSuccessMessage('Link deleted successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
      handleRefresh();
    } catch (err) {
      console.error('Delete error:', err);
      alert(err instanceof Error ? err.message : 'Failed to delete link');
    } finally {
      setDeletingLinkKey(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <RefreshCwIcon className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={handleRefresh}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Scraping Links Management
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCwIcon className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
              <DialogTrigger asChild>
                <Button size="sm" onClick={openAddForm}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Link
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <form onSubmit={handleSubmit}>
                  <DialogHeader>
                    <DialogTitle>
                      {editingLink ? 'Edit Link' : 'Add New Link'}
                    </DialogTitle>
                    <DialogDescription>
                      {editingLink ? 'Update the link details below.' : 'Enter the details for the new scraping link.'}
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <label htmlFor="title" className="text-sm font-medium">
                        Title <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => {
                          setFormData({ ...formData, title: e.target.value });
                          if (formErrors.title) {
                            setFormErrors({ ...formErrors, title: undefined });
                          }
                        }}
                        placeholder="Enter link title (max 100 characters)"
                        maxLength={100}
                        className={formErrors.title ? 'border-red-500' : ''}
                        required
                      />
                      {formErrors.title && (
                        <p className="text-sm text-red-500">{formErrors.title}</p>
                      )}
                    </div>
                    
                    <div className="grid gap-2">
                      <label htmlFor="url" className="text-sm font-medium">
                        URL <span className="text-red-500">*</span>
                      </label>
                      <Textarea
                        id="url"
                        value={formData.url}
                        onChange={(e) => {
                          setFormData({ ...formData, url: e.target.value });
                          if (formErrors.url) {
                            setFormErrors({ ...formErrors, url: undefined });
                          }
                        }}
                        placeholder="Enter the URL to scrape (e.g., https://example.com)"
                        rows={3}
                        className={formErrors.url ? 'border-red-500' : ''}
                        required
                      />
                      {formErrors.url && (
                        <p className="text-sm text-red-500">{formErrors.url}</p>
                      )}
                    </div>
                    
                    <div className="grid gap-2">
                      <label htmlFor="categoryKey" className="text-sm font-medium">
                        Category Key <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="categoryKey"
                        value={formData.categoryKey}
                        onChange={(e) => {
                          setFormData({ ...formData, categoryKey: e.target.value });
                          if (formErrors.categoryKey) {
                            setFormErrors({ ...formErrors, categoryKey: undefined });
                          }
                        }}
                        placeholder="Enter category from the URL (letters, numbers, spaces only)"
                        maxLength={50}
                        className={formErrors.categoryKey ? 'border-red-500' : ''}
                        required
                      />
                      {formErrors.categoryKey && (
                        <p className="text-sm text-red-500">{formErrors.categoryKey}</p>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="isMultiLevel"
                        checked={formData.isMultiLevel}
                        onCheckedChange={(checked) => 
                          setFormData({ ...formData, isMultiLevel: Boolean(checked) })
                        }
                      />
                      <label htmlFor="isMultiLevel" className="text-sm font-medium">
                        Multi Level
                      </label>
                    </div>
                    
                    <div className="grid gap-2">
                      <label htmlFor="serverKey" className="text-sm font-medium">
                        Server <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={formData.serverKey}
                        onValueChange={(value) => {
                          setFormData({ ...formData, serverKey: value });
                          if (formErrors.serverKey) {
                            setFormErrors({ ...formErrors, serverKey: undefined });
                          }
                        }}
                      >
                        <SelectTrigger className={formErrors.serverKey ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select server" />
                        </SelectTrigger>
                        <SelectContent>
                          {linksData?.links.map((link) => (
                            <SelectItem key={link.serverKey} value={link.serverKey}>
                              {link.serverKey}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.serverKey && (
                        <p className="text-sm text-red-500">{formErrors.serverKey}</p>
                      )}
                    </div>
                  </div>
                  
                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsFormOpen(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                          {editingLink ? 'Updating...' : 'Adding...'}
                        </>
                      ) : (
                        `${editingLink ? 'Update' : 'Add'} Link`
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardTitle>
      </CardHeader>

      {successMessage && (
        <div className="mx-6 mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-600 font-medium">{successMessage}</p>
        </div>
      )}

      <CardContent>
        {linksData && (
          <>
            {/* Server Tabs */}
            <Tabs value={activeKey} onValueChange={handleKeyChange} className="w-full">
              <TabsList className="grid w-full grid-cols-auto">
                {linksData.links.map((link) => (
                  <TabsTrigger key={link.serverKey} value={link.serverKey}>
                    {link.serverKey}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {linksData.links.map((serverData) => (
                <TabsContent key={serverData.serverKey} value={serverData.serverKey}>
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-4">
                      <p className="text-sm text-gray-600">
                        Total links: {linksData.data.length} / {linksData.maxLinksLimit}
                      </p>
                    </div>
                    
                    {linksData.data.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No links configured for this server.</p>
                        <Button className="mt-4" onClick={openAddForm}>
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Add First Link
                        </Button>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Title</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Multi Level</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {linksData.data.map((link, index) => (
                            <TableRow key={link.key}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <a
                                    href={link.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {link.title}
                                  </a>
                                  <ExternalLinkIcon className="h-3 w-3 text-gray-400" />
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {link.scrapingSelectors.key}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Checkbox
                                  checked={link.scrapingSelectors.isMultiLevel}
                                  disabled
                                />
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => openEditForm(link)}
                                  >
                                    <PencilIcon className="h-3 w-3 mr-1" />
                                    Edit
                                  </Button>
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button variant="outline" size="sm">
                                        <TrashIcon className="h-3 w-3 mr-1" />
                                        Delete
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>Delete Link</AlertDialogTitle>
                                        <AlertDialogDescription>
                                          Are you sure you want to delete "{link.title}"? This action cannot be undone.
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction
                                          onClick={() => handleDelete(link.key)}
                                          className="bg-red-600 hover:bg-red-700"
                                        >
                                          Delete
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default LinksManagement;
