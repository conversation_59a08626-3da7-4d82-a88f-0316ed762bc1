{"extends": ["next/core-web-vitals"], "ignorePatterns": [".next/**/*", "node_modules/**/*"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}], "@typescript-eslint/no-unused-vars": "warn", "react/no-unescaped-entities": "off", "no-case-declarations": "off"}, "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}]}