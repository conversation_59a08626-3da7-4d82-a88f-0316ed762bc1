{"name": "frontend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/frontend", "projectType": "application", "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/frontend"}, "configurations": {"development": {"outputPath": "apps/frontend"}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "frontend:build", "dev": true}, "configurations": {"development": {"buildTarget": "frontend:build:development", "dev": true}, "production": {"buildTarget": "frontend:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "frontend:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/frontend/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/frontend/**/*.{ts,tsx,js,jsx}"]}}}, "tags": ["type:app", "scope:frontend"]}