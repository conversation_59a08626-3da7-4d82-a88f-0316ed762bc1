{"root": true, "ignorePatterns": ["node_modules/**/*", "dist/**/*", ".nx/**/*"], "plugins": ["@nx"], "extends": ["eslint:recommended"], "env": {"node": true, "es6": true}, "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}]}}, {"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "warn"}}, {"files": ["*.js", "*.jsx"], "rules": {}}]}