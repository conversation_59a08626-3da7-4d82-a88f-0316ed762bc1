{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": []}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}}, "generators": {"@nx/next": {"application": {"style": "css", "linter": "eslint"}}}}